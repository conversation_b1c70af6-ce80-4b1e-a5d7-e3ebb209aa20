---
---
<div class="terminal">
  <div class="terminal-frame">
    <div class="terminal-content">
      <span id="message"></span>
      <span class="caret"></span>
    </div>
    <div class="footer">
      <div>All rights reserved</div>
      <div>Jamcrank 2025</div>
    </div>
    <button class="press-key">(click or press any key)</button>
    <canvas id="matrix-rain" class="matrix-rain"></canvas>
  </div>
</div>

<script>
const messages = [
  'Wake up, Neo...',
  'The matrix has you...',
  'Follow the white rabbit.'
];

let currentMessage = 0;
const messageEl = document.getElementById('message')!;
const canvas = document.getElementById('matrix-rain') as HTMLCanvasElement;
const pressKeyButton = document.querySelector('.press-key') as HTMLButtonElement;
const ctx = canvas.getContext('2d')!;
let waitingForKeyPress = false;

// Type writer effect
async function typeMessage(text: string) {
  messageEl.textContent = '';
  for (const char of text) {
    messageEl.textContent += char;
    await new Promise(resolve => setTimeout(resolve, 200));
  }
}

// Matrix rain effect
function initMatrixRain() {
  canvas.width = window.innerWidth;
  canvas.height = window.innerHeight;
  
  const columns = Math.floor(canvas.width / 20);
  const chars = '日ﾊﾐﾋｰｳｼﾅﾓﾆｻﾜﾂｵﾘｱﾎﾃﾏｹﾒｴｶｷﾑﾕﾗｾﾈｽﾀﾇﾍ'.split('');
  const drops: number[] = new Array(columns).fill(1);
  
  // Slower animation speed
  const animationSpeed = 100; // Increased from 33 to 100ms
  
  function draw() {
    ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    ctx.fillStyle = '#0F0';
    ctx.font = '20px monospace';
    
    for (let i = 0; i < drops.length; i++) {
      const text = chars[Math.floor(Math.random() * chars.length)];
      ctx.fillText(text, i * 20, drops[i] * 20);
      
      if (drops[i] * 20 > canvas.height && Math.random() > 0.99) {
        drops[i] = 0;
      }
      drops[i]++;
    }
  }
  
  return setInterval(draw, animationSpeed);
}

// Start the sequence
async function startSequence() {
  canvas.style.display = 'none';
  
  await typeMessage(messages[0]);
  document.querySelector('.press-key')!.style.display = 'block';
  waitingForKeyPress = true;
  await new Promise(resolve => {
    const progressSequence = () => {
      if (waitingForKeyPress) {
        document.removeEventListener('keydown', handler);
        pressKeyButton.removeEventListener('click', progressSequence);
        pressKeyButton.removeEventListener('touchstart', progressSequence);
        waitingForKeyPress = false;
        document.querySelector('.press-key')!.style.display = 'none';
        resolve(true);
      }
    };
    const handler = (e: KeyboardEvent) => progressSequence();
    document.addEventListener('keydown', handler);
    pressKeyButton.addEventListener('click', progressSequence);
    pressKeyButton.addEventListener('touchstart', progressSequence);
  });
  
  await typeMessage(messages[1]);
  waitingForKeyPress = true;
  document.querySelector('.press-key')!.style.display = 'block';
  await new Promise(resolve => {
    const progressSequence = () => {
      if (waitingForKeyPress) {
        document.removeEventListener('keydown', handler);
        pressKeyButton.removeEventListener('click', progressSequence);
        pressKeyButton.removeEventListener('touchstart', progressSequence);
        waitingForKeyPress = false;
        document.querySelector('.press-key')!.style.display = 'none';
        resolve(true);
      }
    };
    const handler = (e: KeyboardEvent) => progressSequence();
    document.addEventListener('keydown', handler);
    pressKeyButton.addEventListener('click', progressSequence);
    pressKeyButton.addEventListener('touchstart', progressSequence);
  });
  
  await typeMessage(messages[2]);
  await new Promise(resolve => setTimeout(resolve, 5000));
  
  messageEl.textContent = '';
  document.querySelector('.caret')!.style.display = 'none';
  canvas.style.display = 'block';
  setTimeout(() => {
    document.querySelector('.footer')!.style.opacity = '1';
  }, 1000);
  initMatrixRain();
}

// Initialize
window.addEventListener('load', () => {
  startSequence();
});

// Handle window resize
window.addEventListener('resize', () => {
  canvas.width = window.innerWidth;
  canvas.height = window.innerHeight;
});
</script>

<style is:global>
  @import '../styles/terminal.css';
</style>