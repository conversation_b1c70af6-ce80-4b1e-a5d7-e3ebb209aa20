/* Terminal styles */
.terminal {
  font-family: 'Courier New', Courier, monospace;
  background-color: #0C0C0C;
  color: #15ff00;
  height: 100vh;
  width: 100vw;
  padding: 40px;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

.terminal-frame {
  border: 24px solid #1a1a1a;
  height: calc(100% - 48px);
  width: calc(100% - 48px);
  max-width: 1024px;
  max-height: 768px;
  position: relative;
  border-radius: 24px;
  background: #000;
  box-shadow: 
    inset 0 0 18px rgba(21, 255, 0, 0.2),
    0 0 24px rgba(0, 0, 0, 0.5);
  overflow: hidden;
}

/* CRT screen effect */
.terminal-frame::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    rgba(21, 255, 0, 0.1) 50%,
    rgba(0, 0, 0, 0.1) 50%
  );
  background-size: 100% 4px;
  pointer-events: none;
  animation: scanlines 10s linear infinite;
  opacity: 0.6;
  z-index: 2;
}

/* CRT flicker effect */
.terminal-frame::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(21, 255, 0, 0.1);
  opacity: 0;
  z-index: 2;
  pointer-events: none;
  animation: flicker 0.3s infinite;
}

.footer {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 12px;
  color: #15ff00;
  opacity: 0;
  transition: opacity 1s ease;
  text-shadow: 0 0 5px rgba(21, 255, 0, 0.8);
  z-index: 2;
  text-align: center;
}

.terminal-content {
  height: 100%;
  padding: 40px 20px;
  font-size: 24px;
  text-shadow: 0 0 5px rgba(21, 255, 0, 0.8);
  position: relative;
  z-index: 1;
  white-space: pre;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

.caret {
  display: inline-block;
  width: 12px;
  height: 24px;
  background-color: #15ff00;
  margin-left: 4px;
  animation: blink 1s infinite;
  vertical-align: middle;
}

.matrix-rain {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  color: #00ff00;
  font-size: 20px;
  line-height: 20px;
  z-index: 1;
}

.press-key {
  font-size: 14px;
  color: rgba(21, 255, 0, 0.6);
  background: none;
  border: none;
  cursor: pointer;
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: none;
  z-index: 2;
  font-family: 'Courier New', Courier, monospace;
  padding: 10px;
  transition: color 0.3s ease;
}

.press-key:hover {
  color: rgba(21, 255, 0, 0.8);
}

@keyframes scanlines {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(4px);
  }
}

@keyframes flicker {
  0% { opacity: 0.1; }
  5% { opacity: 0.2; }
  10% { opacity: 0.1; }
  15% { opacity: 0.3; }
  25% { opacity: 0.2; }
  30% { opacity: 0.1; }
  35% { opacity: 0.2; }
  100% { opacity: 0.1; }
}